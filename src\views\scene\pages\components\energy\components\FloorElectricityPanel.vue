<template>
  <div class="w-full h-full overflow-auto custom-scrollbar">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center h-full">
      <div class="text-white text-[0.7vw]">加载中...</div>
    </div>

    <!-- 楼层电耗图表 -->
    <div v-else class="w-full h-full">
      <div ref="chartRef" class="w-full h-full" style="min-height: 200px; background: rgba(255, 255, 255, 0.1)"></div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import * as echarts from 'echarts';
  import { getFloorEl } from '/@/api/electricityMeter';

  const loading = ref(false);
  const chartRef = ref(null);
  let chartInstance = null;

  // 楼层电耗数据
  const floorElectricityData = ref([]);

  // 初始化图表
  const initChart = async () => {
    await nextTick();
    if (chartRef.value) {
      console.log('初始化图表，容器尺寸:', chartRef.value.offsetWidth, 'x', chartRef.value.offsetHeight);
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
      console.log('图表实例创建成功:', chartInstance);
    } else {
      console.error('图表容器未找到');
    }
  };

  // 渲染图表
  const renderChart = () => {
    console.log('开始渲染图表');
    console.log('图表实例:', chartInstance);
    console.log('数据长度:', floorElectricityData.value.length);
    console.log('数据内容:', floorElectricityData.value);

    if (!chartInstance) {
      console.error('图表实例不存在');
      return;
    }

    if (!floorElectricityData.value.length) {
      console.error('没有数据');
      return;
    }

    // 按楼层排序数据
    const sortedData = [...floorElectricityData.value].sort((a, b) => {
      const floorA = parseInt(a.floorInfo) || 0;
      const floorB = parseInt(b.floorInfo) || 0;
      return floorA - floorB;
    });

    console.log('排序后的数据:', sortedData);

    const option = {
      grid: {
        top: '15%',
        left: '15%',
        right: '10%',
        bottom: '20%',
      },
      xAxis: {
        type: 'category',
        data: sortedData.map((item) => `${item.floorInfo}F`),
        axisLabel: {
          color: '#fff',
          fontSize: 10,
        },
        axisLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.3)' },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          formatter: (value) => {
            if (value >= 1000) {
              return (value / 1000).toFixed(1) + 'k';
            }
            return value;
          },
        },
        axisLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.3)' },
        },
        splitLine: {
          lineStyle: { color: 'rgba(255, 255, 255, 0.1)' },
        },
      },
      series: [
        {
          name: '楼层电耗',
          type: 'bar',
          data: sortedData.map((item) => parseFloat(item.valueData) || 0),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#3B8EE6' },
              { offset: 1, color: '#1E40AF' },
            ]),
          },
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 9,
            formatter: (params) => {
              const value = parseFloat(params.value);
              if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'k';
              }
              return Math.round(value);
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#3B8EE6',
        textStyle: {
          color: '#fff',
        },
        formatter: (params) => {
          const data = params[0];
          const value = parseFloat(data.value);
          const unit = data.seriesName.includes('电耗') ? 'kW' : 'kWh';
          return `${data.name}<br/>电耗: ${value.toLocaleString()} ${unit}`;
        },
      },
    };

    console.log('设置图表配置:', option);
    chartInstance.setOption(option);
    console.log('图表渲染完成');
  };

  // 加载楼层电耗数据
  const loadFloorElectricityData = async () => {
    try {
      loading.value = true;

      // 先使用模拟数据进行测试
      console.log('开始加载楼层电耗数据');

      // 暂时直接使用模拟数据，确保图表能显示
      floorElectricityData.value = [
        {
          valueData: 183224.0,
          dataTime: null,
          type: null,
          signalName: null,
          describe: 'KW',
          deviceName: null,
          floorInfo: '1',
          room: null,
        },
        {
          valueData: 41504.0,
          dataTime: null,
          type: null,
          signalName: null,
          describe: 'KW',
          deviceName: null,
          floorInfo: '3',
          room: null,
        },
        {
          valueData: 95600.0,
          dataTime: null,
          type: null,
          signalName: null,
          describe: 'KW',
          deviceName: null,
          floorInfo: '2',
          room: null,
        },
        {
          valueData: 67800.0,
          dataTime: null,
          type: null,
          signalName: null,
          describe: 'KW',
          deviceName: null,
          floorInfo: '4',
          room: null,
        },
      ];

      console.log('使用模拟数据:', floorElectricityData.value);
      renderChart();

      // 注释掉API调用，先确保图表能正常显示
      /*
      const result = await getFloorEl();
      console.log('楼层电耗数据:', result);

      // 根据您的说明，接口直接返回数组数据
      if (Array.isArray(result)) {
        floorElectricityData.value = result;
      } else {
        // 如果返回的是包装对象，尝试获取数组数据
        floorElectricityData.value = result.result || result.data || [];
      }

      // 验证数据格式
      if (floorElectricityData.value.length > 0) {
        console.log('处理后的楼层电耗数据:', floorElectricityData.value);
        renderChart();
      } else {
        console.warn('没有楼层电耗数据');
        message.warning('暂无楼层电耗数据');
      }
      */
    } catch (error) {
      console.error('加载楼层电耗数据失败:', error);
      message.error('加载楼层电耗数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  onMounted(async () => {
    console.log('组件挂载');
    await initChart();
    await loadFloorElectricityData();
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
</style>
